package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frontapi.entity.FrontUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import java.math.BigDecimal;
import org.apache.ibatis.annotations.Select;
import java.util.List;
import java.util.Map;

@Mapper
public interface FrontUserMapper extends BaseMapper<FrontUser> {
    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    FrontUser selectUserIdByPhone(@Param("phone") String phone);

    @Update("UPDATE front_user SET available_balance = available_balance - #{amount} " +
            "WHERE id = #{userId} AND available_balance >= #{amount}")
    int deductBalance(Long userId, BigDecimal amount);

    @Update("UPDATE front_user SET copy_trade_balance = copy_trade_balance - #{amount} " +
            "WHERE id = #{userId} AND copy_trade_balance >= #{amount}")
    int decreaseCopyTradeBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE front_user SET copy_trade_balance = copy_trade_balance + #{amount} " +
            "WHERE id = #{userId}")
    int increaseCopyTradeBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE front_user SET profit_balance = profit_balance + #{amount} " +
            "WHERE id = #{userId}")
    int increaseProfitBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE front_user SET reserve_amount = reserve_amount + #{amount} " +
            "WHERE id = #{userId}")
    int increaseReserveAmount(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Update("UPDATE front_user SET commission_balance = commission_balance + #{amount} " +
            "WHERE id = #{userId}")
    int increaseCommissionBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    @Select("SELECT username FROM front_user WHERE id = #{userId}")
    String getUsernameById(@Param("userId") Long userId);

    @Select("SELECT email FROM front_user WHERE id = #{userId}")
    String getEmailById(@Param("userId") Long userId);

    @Select("SELECT copy_trade_balance FROM front_user WHERE id = #{userId}")
    BigDecimal getCopyTradeBalanceById(@Param("userId") Long userId);

    @Select("SELECT share_code, referrer_code, is_following as isCopyTrade, commission_rate FROM front_user WHERE id = #{userId}")
    com.frontapi.vo.UserReferralVO getUserReferralInfo(@Param("userId") Long userId);

    @Select("SELECT id as userId, share_code, referrer_code, is_following as isCopyTrade, commission_rate FROM front_user WHERE share_code = #{shareCode}")
    com.frontapi.vo.UserReferralVO getUserByShareCode(@Param("shareCode") String shareCode);

    BigDecimal selectAvailableBalance(@Param("userId") Long userId);
    int decreaseAvailableBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    String selectUsernameById(@Param("userId") Long userId);

    List<Map<String, Object>> selectLeadersWithConfigAndOrderStats();

    /**
     * 查询跟随指定带单员的用户列表
     */
    @Select("SELECT id, user_no, username, copy_trade_balance FROM front_user " +
            "WHERE leader_id = #{leaderId} AND is_following = 1 AND status = 1")
    List<com.frontapi.entity.FrontUser> selectFollowersByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 清除用户跟单关系（仅修改状态，不影响账户余额）
     * 用于结算完成后的关系清除
     */
    @Update("UPDATE front_user SET is_following = 0, leader_id = 0, follow_start_time = NULL,copy_trade_frozen_status=0, update_time = NOW() WHERE id = #{userId}")
    int clearFollowRelationStatusOnly(@Param("userId") Long userId);

    /**
     * 提现时更新用户余额（扣除可用余额，增加冻结余额）
     */
    @Update("UPDATE front_user SET available_balance = available_balance - #{amount}, frozen_balance = frozen_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int updateBalanceForWithdraw(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 转账时更新转出方余额（扣除可用余额）
     */
    @Update("UPDATE front_user SET available_balance = available_balance - #{amount}, update_time = NOW() WHERE id = #{userId}")
    int decreaseAvailableBalanceForTransfer(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 转账时更新接收方余额（增加可用余额）
     */
    @Update("UPDATE front_user SET available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int increaseAvailableBalanceForTransfer(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：资金账户到佣金账户
     */
    @Update("UPDATE front_user SET available_balance = available_balance - #{amount}, commission_balance = commission_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromFundToCommission(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：佣金账户到资金账户
     */
    @Update("UPDATE front_user SET commission_balance = commission_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromCommissionToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：资金账户到跟单账户
     */
    @Update("UPDATE front_user SET available_balance = available_balance - #{amount}, copy_trade_balance = copy_trade_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromFundToCopy(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：跟单账户到资金账户
     */
    @Update("UPDATE front_user SET copy_trade_balance = copy_trade_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromCopyToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：资金账户到收益账户
     */
    @Update("UPDATE front_user SET available_balance = available_balance - #{amount}, profit_balance = profit_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromFundToProfit(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：收益账户到资金账户
     */
    @Update("UPDATE front_user SET profit_balance = profit_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromProfitToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：收益账户到跟单账户
     */
    @Update("UPDATE front_user SET profit_balance = profit_balance - #{amount}, copy_trade_balance = copy_trade_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromProfitToCopy(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    /**
     * 账户划转：佣金账户到跟单账户
     */
    @Update("UPDATE front_user SET commission_balance = commission_balance - #{amount}, copy_trade_balance = copy_trade_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
    int transferFromCommissionToCopy(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
}
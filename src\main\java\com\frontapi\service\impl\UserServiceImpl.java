package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.LoginResponse;
import com.frontapi.dto.RegisterRequest;
import com.frontapi.dto.UpdatePasswordDTO;
import com.frontapi.dto.UpdateSecurityPasswordDTO;
import com.frontapi.dto.UpdatePhoneDTO;
import com.frontapi.entity.FrontUser;
import com.frontapi.exception.BusinessException;
import com.frontapi.mapper.UserMapper;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.service.SmsService;
import com.frontapi.service.UserService;
import com.frontapi.util.GeneratorUtil;
import com.frontapi.util.JwtUtil;
import com.frontapi.util.SecurityUtils;
import com.frontapi.vo.UserShareVO;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.util.concurrent.TimeUnit;
import com.frontapi.service.MailService;
import com.frontapi.entity.AccountTransferRecord;
import com.frontapi.mapper.AccountTransferRecordMapper;
import com.frontapi.vo.TeamStatsVO;
import com.frontapi.vo.TeamRecordVO;
import com.frontapi.vo.PageResult;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final SmsService smsService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private MailService mailService;
    @Autowired
    private AccountTransferRecordMapper accountTransferRecordMapper;
    @Autowired
    private FrontUserMapper frontUserMapper;

 

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean register(RegisterRequest request) {
        System.out.println(request);
        // 1. 手机号/邮箱二选一必填，且格式校验
        boolean isPhone = org.springframework.util.StringUtils.hasText(request.getPhone());
        boolean isEmail = org.springframework.util.StringUtils.hasText(request.getEmail());
        if (!isPhone && !isEmail) {
            throw new BusinessException("手机号或邮箱必填其一");
        }
        if (isPhone) {
            if (!request.getPhone().matches("^1[3-9]\\d{9}$")) {
                throw new BusinessException("手机号格式不正确");
            }
            if (userMapper.existsByPhone(request.getPhone())) {
                throw new BusinessException("手机号已被注册");
            }
        }
        if (isEmail) {
            if (!request.getEmail().matches("^\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
                throw new BusinessException("邮箱格式不正确");
            }
            if (userMapper.existsByEmail(request.getEmail())) {
                throw new BusinessException("邮箱已被注册");
            }
        }

        // 验证邀请码是否存在
        if (!userMapper.existsByReferrerCode(request.getReferrerCode())) {
            throw new BusinessException("无效的邀请码");
        }
        String key = "register:code:email:" + request.getEmail();
        Object savedCode = redisTemplate.opsForValue().get(key);
        System.out.println("保存的"+savedCode);
        System.out.println("获取的"+request.getEmailCode());
        if (savedCode == null || !request.getEmailCode().equals(savedCode.toString())) {
            throw new BusinessException("验证码错误或已过期");
        }
 

        FrontUser user = new FrontUser();
        user.setUsername(request.getUsername());
//        user.setPhone(isPhone ? request.getPhone() : null);
        user.setEmail(isEmail ? request.getEmail() : null);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRealName(request.getRealName());
        user.setReferrerCode(request.getReferrerCode());
        user.setSecurityPassword(passwordEncoder.encode(request.getSecurityPassword()));

        // 生成唯一9位数字userNo
        String userNo = generateUniqueUserNo9Digits();
        user.setUserNo(userNo);
        // String shareCode = generateUniqueShareCode();
        user.setShareCode(userNo);

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        user.setUpdateTime(now);
        user.setCreateTime(now);

        // 账户状态和安全设置
        user.setStatus(true);  // 账号状态，true为正常
        user.setIsActivated(false);  // 是否激活，false为未激活

        // 账户金额相关 - 明确设置初始值
        user.setAvailableBalance(BigDecimal.ZERO);  // 资金账户
        user.setCopyTradeBalance(BigDecimal.ZERO);  // 跟单账户
        user.setCommissionBalance(BigDecimal.ZERO);  // 佣金账户

        user.setProfitBalance(BigDecimal.ZERO);  // 利润账户
        user.setFrozenBalance(BigDecimal.ZERO);  // 提现冻结余额

        // 团队相关
        user.setTeamTotalCount(0);  // 团队总账户数
        user.setTeamTodayCount(0);  // 团队今日新增账户数

        user.setCommissionRate(BigDecimal.ZERO);  // 佣金比例
        user.setTotalRecharge(BigDecimal.ZERO);  // 累积充值
        
        // 其他字段会使用实体类中的默认值和自动填充
        int rows = userMapper.insert(user);

        if (rows > 0) {
            // 通过分享码递归查找推荐人链并更新团队统计
            updateReferrerTeamCounts(request.getReferrerCode());             
            //这是是不是要删除掉redis里面存的手机验证码
            String smsKey = "register:code:email:" + request.getEmail();
//            String smsKey = "sms:verify:" + request.getPhone();
            redisTemplate.delete(smsKey);           
            
            return true;
        }
        
        return false;
    }



    // 生成唯一9位数字userNo
    private String generateUniqueUserNo9Digits() {
        String userNo;
        do {
            userNo = String.valueOf(100000000 + (long)(Math.random() * 900000000));
        } while (userMapper.existsByUserNo(userNo));
        return userNo;
    }
 

    @Override
    public LoginResponse login(String phone, String email, String password) {
        FrontUser user = null;
        if (org.springframework.util.StringUtils.hasText(phone)) {
            user = userMapper.findByPhone(phone);
        } else if (org.springframework.util.StringUtils.hasText(email)) {
            user = userMapper.findByEmail(email);
        }
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        if (!user.getStatus()) {
            throw new BusinessException("账号已被锁定");
        }
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException("密码错误");
        }
        String token = jwtUtil.generateToken(user.getUserNo());
        UserVO userVO = convertToVO(user);
        return new LoginResponse(token, userVO);
    }

    @Override
    public LoginResponse loginByCode(String phone, String code) {
        if (!smsService.verifyCode(phone, code)) {
            throw new BusinessException("验证码错误");
        }
        
        FrontUser user = userMapper.findByPhone(phone);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        if (!user.getStatus()) {
            throw new BusinessException("账号已被锁定");
        }
        
        String token = jwtUtil.generateToken(phone);
        UserVO userVO = convertToVO(user);
        
        return new LoginResponse(token, userVO);
    }

    private UserVO convertToVO(FrontUser user) {
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);
        return vo;
    }

    @Override
    public UserVO getCurrentUserInfo() {
        // 使用 SecurityUtils 工具类获取userNo
        String userNo = SecurityUtils.getCurrentUserNo();
        if (userNo == null) {
            throw new BusinessException("用户未登录");
        }

        // 通过userNo查找用户
        FrontUser user = userMapper.findByUserNo(userNo);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 使用相同的转换方法
        return convertToVO(user);
    }

    @Override
    public Page<UserShareVO> getShareList(String shareCode, Integer page, Integer size) {
         Page<UserShareVO> paging = new Page<>(page, size);
         return userMapper.selectShareList(paging, shareCode);
   }

    @Override
    public boolean sendPasswordVerifyCode() {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null || StringUtils.isEmpty(currentUser.getPhone())) {
            return false;
        }
        
        // 2. 生成验证码
        String code = generateVerifyCode();
        
        // 3. 保存验证码到Redis，设置5分钟过期
        String key = "password:verify:" + currentUser.getPhone();
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        
        // 4. 发送验证码短信
        try {
            return smsService.sendVerifyCode(currentUser.getPhone(), code);
        } catch (Exception e) {
            // 统一使用简单的错误提示
            throw new BusinessException("发送失败");
        }
    }

    @Override
    public boolean updatePassword(UpdatePasswordDTO dto) {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null || StringUtils.isEmpty(currentUser.getEmail())) {
            throw new BusinessException("用户未登录");
        }
        
        // // 2. 验证验证码
        // String key = "password:verify:" + currentUser.getPhone();
        // Object savedCode = redisTemplate.opsForValue().get(key);
        // if (savedCode == null || !dto.getVerifyCode().equals(savedCode.toString())) {
        //     throw new BusinessException("验证码错误或已过期");
        // }
        //邮箱验证
        String key = "reset:code:email:" + currentUser.getEmail();
        Object savedCode = redisTemplate.opsForValue().get(key);
        if (savedCode == null || !dto.getVerifyCode().equals(savedCode.toString())) {
            throw new BusinessException("验证码错误或已过期");
        }
        
        // 3. 使用XML SQL语句更新密码
        String encodedPassword = passwordEncoder.encode(dto.getPassword());
        int rows = userMapper.updateUserPassword(currentUser.getId(), encodedPassword);
        boolean success = rows > 0;
        
        // 4. 删除验证码
        if (success) {
            redisTemplate.delete(key);
        } else {
            throw new BusinessException("密码更新失败");
        }
        
        return success;
    }

    @Override
    public boolean sendSecurityPasswordVerifyCode() {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null || StringUtils.isEmpty(currentUser.getEmail())) {
            throw new BusinessException("用户未登录");
        }
        
        // 2. 生成验证码
        String code = generateVerifyCode();
        
        // 3. 保存验证码到Redis，设置5分钟过期
        String key = "security:password:verify:" + currentUser.getPhone();
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        
        // 4. 发送验证码短信
        try {
            return smsService.sendVerifyCode(currentUser.getPhone(), code);
        } catch (Exception e) {
            throw new BusinessException("发送失败");
        }
    }

    @Override
    public boolean updateSecurityPassword(UpdateSecurityPasswordDTO dto) {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null || StringUtils.isEmpty(currentUser.getEmail())) {
            throw new BusinessException("用户未登录");
        }
        
        // 2. 验证验证码
        // String key = "security:password:verify:" + currentUser.getPhone();        
        String key = "reset:code:email:" + currentUser.getEmail();
        Object savedCode = redisTemplate.opsForValue().get(key);
        if (savedCode == null || !dto.getVerifyCode().equals(savedCode.toString())) {
            throw new BusinessException("验证码错误或已过期");
        }
        
        // 3. 使用XML SQL语句更新安全密码
        String encodedSecurityPassword = passwordEncoder.encode(dto.getSecurityPassword());
        int rows = userMapper.updateUserSecurityPassword(currentUser.getId(), encodedSecurityPassword);
        boolean success = rows > 0;
        
        // 4. 删除验证码
        if (success) {
            redisTemplate.delete(key);
        } else {
            throw new BusinessException("安全密码更新失败");
        }
        
        return success;
    }

    @Override
    public boolean sendPhoneVerifyCode(String newPhone) {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 2. 验证新手机号格式
        if (!newPhone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        
        // 3. 检查新手机号是否已被使用
        if (userMapper.existsByPhone(newPhone)) {
            throw new BusinessException("该手机号已被使用");
        }
        
        // 4. 生成验证码
        String code = generateVerifyCode();
        
        // 5. 保存验证码到Redis，设置5分钟过期
        String key = "phone:update:verify:" + currentUser.getPhone() + ":" + newPhone;
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        
        // 6. 发送验证码短信
        try {
            return smsService.sendVerifyCode(newPhone, code);
        } catch (Exception e) {
            throw new BusinessException("发送失败");
        }
    }

    @Override
    public boolean updatePhone(UpdatePhoneDTO dto) {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 2. 验证新手机号格式
        if (!dto.getNewPhone().matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        
        // 3. 检查新手机号是否已使用
        if (userMapper.existsByPhone(dto.getNewPhone())) {
            throw new BusinessException("该手机号已被使用");
        }
        
        // 4. 验证验证码
        String key = "phone:update:verify:" + currentUser.getPhone() + ":" + dto.getNewPhone();
        Object savedCode = redisTemplate.opsForValue().get(key);
        if (savedCode == null || !dto.getVerifyCode().equals(savedCode.toString())) {
            throw new BusinessException("验证码错误或已过期");
        }
        
        // 5. 更手机号
        FrontUser user = new FrontUser();
        user.setId(currentUser.getId());
//        user.setPhone(dto.getNewPhone());
        LocalDateTime now = LocalDateTime.now();
        user.setUpdateTime(now);
        
        int rows = userMapper.updateById(user);
        boolean success = rows > 0;
        
        // 6. 删除验证码
        if (success) {
            redisTemplate.delete(key);
        } else {
            throw new BusinessException("手机号更新失败");
        }
        
        return success;
    }

    @Override
    public boolean verifySecurityPassword(String securityPassword) {
        // 1. 获取当前登录用户
        UserVO currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 2. 获取用户完整信息（包含安全密码）
        FrontUser user = userMapper.selectById(currentUser.getId());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 3. 检查用户是否设置了安全密码
        if (StringUtils.isEmpty(user.getSecurityPassword())) {
            throw new BusinessException("请先设置安全密码");
        }
        
        // 4. 验证安全密码是否正确
        try {
            return passwordEncoder.matches(securityPassword, user.getSecurityPassword());
        } catch (Exception e) {
            // log.error("验证安全密码失败", e);
            throw new BusinessException("验证安全密码失败");
        }
    }

    private String generateVerifyCode() {
        // 生成6位数字验证码
        return RandomStringUtils.randomNumeric(6);
    }

    /**
     * 递归更新上级推荐人的团队统计
     * @param referrerCode 推荐人分享码
     */
    private void updateReferrerTeamCounts(String referrerCode) {
        String currentShareCode = referrerCode;
        while (currentShareCode != null && !currentShareCode.isEmpty()) {
            // 查找推荐人
            FrontUser referrer = userMapper.findByShareCode(currentShareCode);
            if (referrer == null) {
                break;
            }
            // 更新推荐人的团队统计
            userMapper.incrementTeamCounts(referrer.getId());

            // 继续查找上级推荐人
            currentShareCode = referrer.getReferrerCode();
        }
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userMapper.existsByPhone(phone);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.existsByEmail(email);
    }

    @Override
    public void sendResetCode(String phone, String email) {
        String code = org.apache.commons.lang3.RandomStringUtils.randomNumeric(6);
        String key;
        if (org.springframework.util.StringUtils.hasText(phone)) {
            key = "reset:code:phone:" + phone;
            log.info("【重置密码验证码-手机】{}: {}", phone, code);
        } else if (org.springframework.util.StringUtils.hasText(email)) {
            key = "reset:code:email:" + email;
            // 发送邮件
            log.info(email);
            mailService.sendVerifyCode(email, "重置密码验证码", code);
            log.info("【重置密码验证码-邮箱】{}: {}", email, code);
        } else {
            throw new BusinessException("手机号或邮箱必填其一");
        }
        redisTemplate.opsForValue().set(key, code, 10, java.util.concurrent.TimeUnit.MINUTES);
    }

    @Override
    public void resetPassword(String phone, String email, String code, String newPassword) {
        String key;
        FrontUser user = null;
        if (org.springframework.util.StringUtils.hasText(phone)) {
            key = "reset:code:phone:" + phone;
            user = userMapper.findByPhone(phone);
        } else if (org.springframework.util.StringUtils.hasText(email)) {
            key = "reset:code:email:" + email;
            user = userMapper.findByEmail(email);

        } else {
            throw new BusinessException("手机号或邮箱必填其一");
        }
        Object realCode = redisTemplate.opsForValue().get(key);
        if (realCode == null || !realCode.toString().equals(code)) {
            throw new BusinessException("验证码错误或已过期");
        }
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        String encodedPwd = passwordEncoder.encode(newPassword);
        userMapper.updatePassword(user.getId(), encodedPwd);
        redisTemplate.delete(key);
    }

    @Override
    public void sendRegisterEmailCode(String email) {
        if (StringUtils.isEmpty(email)) {
            throw new RuntimeException("邮箱不能为空");
        }
        String code = org.apache.commons.lang3.RandomStringUtils.randomNumeric(6);
        String key = "register:code:email:" + email;

        // 存入Redis，有效期5分钟
        redisTemplate.opsForValue().set(key, code, 5, java.util.concurrent.TimeUnit.MINUTES);
        // 发送邮件
        mailService.sendVerifyCode(email, "注册验证码", code);
        log.info("【注册邮箱验证码】{}: {}", email, code);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTransfer(Long userId, String fromAccountType, String toAccountType, BigDecimal amount, String payPassword) {
        try {
            // 1. 获取用户信息
            FrontUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 4.1 跟单账户冻结校验
            if ("copy".equals(fromAccountType)) {
                if (user.getCopyTradeFrozenStatus() != null && user.getCopyTradeFrozenStatus() != 0) {
                    throw new RuntimeException("跟单账户已冻结，无法操作");
                }
            }

            // 2. 验证支付密码
            if (!passwordEncoder.matches(payPassword, user.getSecurityPassword())) {
                throw new RuntimeException("支付密码错误");
            }
            
            // 3. 验证划转规则（在Controller中已经验证，这里再次确认）
            if (!validateTransferRule(user, fromAccountType, toAccountType)) {
                throw new RuntimeException("该账户类型不支持此划转");
            }
            
            // 4. 获取转出账户余额
            BigDecimal fromBalance = getAccountBalance(user, fromAccountType);
            if (fromBalance.compareTo(amount) < 0) {
                throw new RuntimeException("转出账户余额不足");
            }
            
            
            // 记录划转前的余额
            BigDecimal toBalance = getAccountBalance(user, toAccountType);
            
            // 5. 执行划转（使用SQL命令，只修改相关字段）
            int transferResult = executeAccountTransfer(userId, fromAccountType, toAccountType, amount);
            if (transferResult <= 0) {
                throw new RuntimeException("账户划转失败");
            }
            
            // 7. 保存划转记录
            AccountTransferRecord record = new AccountTransferRecord();
            record.setUserId(userId);
            record.setUsername(user.getUsername());
            record.setFromAccountType(fromAccountType);
            record.setToAccountType(toAccountType);
            record.setAmount(amount);
            record.setFromBalanceBefore(fromBalance);
            record.setFromBalanceAfter(fromBalance.subtract(amount));
            record.setToBalanceBefore(toBalance);
            record.setToBalanceAfter(toBalance.add(amount));
            record.setStatus(1);
            accountTransferRecordMapper.insert(record);
            
            log.info("用户{}账户划转成功: 从{}到{}, 金额: {}", 
                user.getUsername(), fromAccountType, toAccountType, amount);
            
            return true;
            
        } catch (Exception e) {
            log.error("账户划转失败: {}", e.getMessage());
            throw new RuntimeException("划转失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证划转规则
     */
    private boolean validateTransferRule(FrontUser user, String fromAccountType, String toAccountType) {
        // 资金账户 ↔ 跟单账户（双向）
        if (("fund".equals(fromAccountType) && "copy".equals(toAccountType)) ||
            ("copy".equals(fromAccountType) && "fund".equals(toAccountType))) {
            return true;
        }

        // 佣金账户 → 资金账户或跟单账户
        if ("commission".equals(fromAccountType) &&
            ("fund".equals(toAccountType) || "copy".equals(toAccountType))) {
            return true;
        }

        // 利润账户 → 资金账户或跟单账户
        if ("profit".equals(fromAccountType) &&
            ("fund".equals(toAccountType) || "copy".equals(toAccountType))) {
            // 检查利润账户划转开关
            if ( user.getProfitTransferEnabled() != 1) {
                return false; // 利润账户划转被禁用
            }
            return true;
        }

        // 其他情况都不允许
        return false;
    }
    
    /**
     * 获取账户余额
     */
    private BigDecimal getAccountBalance(FrontUser user, String accountType) {
        switch (accountType) {
            case "fund":
                return user.getAvailableBalance();
            case "commission":
                return user.getCommissionBalance();
            case "copy":
                return user.getCopyTradeBalance();
            case "profit":
                return user.getProfitBalance();
            default:
                return BigDecimal.ZERO;
        }
    }
    
    /**
     * 执行账户划转（使用SQL命令，只修改相关字段）
     */
    private int executeAccountTransfer(Long userId, String fromAccountType, String toAccountType, BigDecimal amount) {
        String transferKey = fromAccountType + "_to_" + toAccountType;

        switch (transferKey) {
            case "fund_to_commission":
                return frontUserMapper.transferFromFundToCommission(userId, amount);
            case "commission_to_fund":
                return frontUserMapper.transferFromCommissionToFund(userId, amount);
            case "fund_to_copy":
                return frontUserMapper.transferFromFundToCopy(userId, amount);
            case "copy_to_fund":
                return frontUserMapper.transferFromCopyToFund(userId, amount);
            case "fund_to_profit":
                return frontUserMapper.transferFromFundToProfit(userId, amount);
            case "profit_to_fund":
                return frontUserMapper.transferFromProfitToFund(userId, amount);
            default:
                throw new RuntimeException("不支持的账户划转类型: " + fromAccountType + " -> " + toAccountType);
        }
    }

    @Override
    public TeamStatsVO getTeamStats(Long userId) {
        FrontUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        String shareCode = user.getShareCode();
        TeamStatsVO vo = new TeamStatsVO();
        vo.setTodayRecommend(userMapper.countTodayRecommend(shareCode));
        vo.setDirectTotal(userMapper.countDirectTotal(shareCode));
        vo.setValidDirect(userMapper.countValidDirect(shareCode));
        vo.setTeamTotal(user.getTeamTotalCount() == null ? 0 : user.getTeamTotalCount());
        return vo;
    }

    @Override
    public PageResult<TeamRecordVO> getTeamRecords(Long userId, String type, Integer page, Integer size) {
        FrontUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        String shareCode = user.getShareCode();
        Page<TeamRecordVO> pageObj = new Page<>(page, size);
        Page<TeamRecordVO> resultPage = userMapper.selectTeamRecords(pageObj, shareCode, type);
        PageResult<TeamRecordVO> result = new PageResult<>();
        result.setRecords(resultPage.getRecords());
        result.setTotal(resultPage.getTotal());
        return result;
    }

    @Override
@Transactional
public void assignCommission(UserVO currentUser, String userNo, BigDecimal assignRate) {
    // 1. 查询被分配用户
    FrontUser targetUser = userMapper.selectByUserNo(userNo);
    if (targetUser == null) throw new RuntimeException("被分配用户不存在");

    // 2. 校验当前用户激活状态
    FrontUser currentUserEntity = userMapper.selectById(currentUser.getId());
    if (currentUserEntity.getIsActivated() == null || !currentUserEntity.getIsActivated()) {
        throw new RuntimeException("您未激活，无权限分配佣金比例");
    }

    // 3. 校验被分配用户激活状态
    if (targetUser.getIsActivated() == null || !targetUser.getIsActivated()) {
        throw new RuntimeException("该用户未激活，无权限分配佣金比例");
    }

    // 4. 校验直推关系
    if (!currentUser.getShareCode().equals(targetUser.getReferrerCode())) {
        throw new RuntimeException("只能分配给自己的直推成员");
    }

    // 5. 校验被分配用户佣金比例为0
    if (targetUser.getCommissionRate() != null && targetUser.getCommissionRate().compareTo(BigDecimal.ZERO) > 0) {
        throw new RuntimeException("该用户已分配佣金比例，不能再次分配");
    }

    // 6. 校验分配比例的有效性
    if (assignRate == null || assignRate.compareTo(BigDecimal.ZERO) <= 0) {
        throw new RuntimeException("分配比例必须大于0");
    }

    if (assignRate.compareTo(new BigDecimal("100")) > 0) {
        throw new RuntimeException("分配比例不能超过100%");
    }

    // 7. 校验分配比例不能超过当前用户的佣金比例
    if (currentUserEntity.getCommissionRate() == null ||
        assignRate.compareTo(currentUserEntity.getCommissionRate()) > 0) {
        throw new RuntimeException("分配比例不能超过您的佣金比例: " +
            (currentUserEntity.getCommissionRate() != null ? currentUserEntity.getCommissionRate() + "%" : "0%"));
    }

    // 8. 更新被分配用户佣金比例（不再减掉自己的佣金比例）
    int updateTarget = userMapper.updateCommissionRateByUserNo(userNo, assignRate, BigDecimal.ZERO);
    if (updateTarget == 0) throw new RuntimeException("分配失败，用户状态已变更");

    log.info("佣金比例分配成功 - 分配人: {}(佣金比例:{}%), 被分配人: {}, 分配比例: {}%",
            currentUser.getUsername(), currentUserEntity.getCommissionRate(),
            targetUser.getUsername(), assignRate);
}

    @Override
    public UserVO selectByUserNo(String userNo) {
        FrontUser user = userMapper.selectByUserNo(userNo);
        return user != null ? convertToVO(user) : null;
    }
    @Override
    public UserVO getUserByEmail(String email) {
        FrontUser user = userMapper.findByEmail(email);
        return user != null ? convertToVO(user) : null;
    }
    
    @Override
    public void checkPayPassword(Long userId, String inputPassword) {
        FrontUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        if (!passwordEncoder.matches(inputPassword, user.getSecurityPassword())) {
            throw new BusinessException("支付密码错误");
        }
    }

    @Override
    public void updateAvatar(Long userId, String avatarUrl) {
        userMapper.updateAvatar(userId, avatarUrl);
    }
} 
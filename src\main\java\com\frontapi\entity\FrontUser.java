package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("front_user")
public class FrontUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userNo;
    private String username;
    private String password;
    private String email;
    private String securityPassword;
    private String realName;
    // private String phone;
    private String shareCode;
    private String referrerCode;
    private String avatar;
    
    // 账户余额相关字段
    private BigDecimal availableBalance = BigDecimal.ZERO;
    private BigDecimal copyTradeBalance = BigDecimal.ZERO;
    private BigDecimal commissionBalance = BigDecimal.ZERO;
    
    private Integer copyTradeFrozenStatus;
    private BigDecimal profitBalance = BigDecimal.ZERO;
    private BigDecimal frozenBalance = BigDecimal.ZERO;
    
    // CAT币余额
    private BigDecimal catBalance = BigDecimal.ZERO;
    
    // 团队统计字段
    private Integer teamTotalCount = 0;
    private Integer teamTodayCount = 0;
    
    // 状态字段
    private Boolean status = true;
    
    // 佣金相关字段
    private BigDecimal commissionRate = BigDecimal.ZERO;
    private BigDecimal totalRecharge = BigDecimal.ZERO;
    private Boolean isActivated = false;
    
    // 时间字段
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    // 非数据库字段
    @TableField(exist = false)
    private String levelName;

    private Integer isLeader;
    private Integer isFollowing;
    private Date followStartTime;
    private Long copyConfigId;
    private Long leaderId;

    // 利润账户划转控制
    private Integer profitTransferEnabled = 1;
} 
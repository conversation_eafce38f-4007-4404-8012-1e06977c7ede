package com.frontapi.service.impl;

import com.frontapi.entity.CopyConfig;
import com.frontapi.entity.FrontUser;
import com.frontapi.mapper.CopyConfigMapper;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.service.FollowRelationCleanupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 跟单关系清除服务实现类
 */
@Slf4j
@Service
public class FollowRelationCleanupServiceImpl implements FollowRelationCleanupService {

    @Autowired
    private FrontUserMapper frontUserMapper;
    
    @Autowired
    private CopyConfigMapper copyConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupInvalidFollowRelations(Long leaderId) {
        try {
            log.info("开始清除带单员ID: {} 的无效跟单关系", leaderId);
            
            // 1. 查询所有跟随该带单员的用户
            List<FrontUser> followers = frontUserMapper.selectFollowersByLeaderId(leaderId);
            
            if (followers.isEmpty()) {
                log.info("带单员ID: {} 没有跟单用户", leaderId);
                return 0;
            }
            
            log.info("找到 {} 个跟单用户，开始验证余额条件", followers.size());
            
            // 2. 获取带单员的配置信息
            FrontUser leader = frontUserMapper.selectById(leaderId);
            if (leader == null || leader.getCopyConfigId() == null) {
                log.warn("带单员ID: {} 不存在或未配置跟单参数", leaderId);
                return 0;
            }
            
            CopyConfig config = copyConfigMapper.selectById(leader.getCopyConfigId());
            if (config == null) {
                log.warn("带单员ID: {} 的配置不存在", leaderId);
                return 0;
            }
            
            log.info("带单员配置 - 最低跟单金额: {}, 最高跟单金额: {}", 
                    config.getMinFollowAmount(), config.getMaxFollowAmount());
            
            int cleanupCount = 0;
            
            // 3. 逐个检查跟单用户的余额条件
            for (FrontUser follower : followers) {
                boolean shouldCleanup = checkFollowerBalance(follower, config);
                
                if (shouldCleanup) {
                    // 清除跟单关系
                    cleanupFollowRelation(follower);
                    cleanupCount++;
                    
                    log.info("清除跟单关系 - 用户ID: {}, 用户名: {}, 余额: {}, 原因: 不满足跟单金额条件，已解锁账户",
                            follower.getId(), follower.getUsername(), follower.getCopyTradeBalance());
                }
            }
            
            log.info("带单员ID: {} 的跟单关系清除完成，共清除 {} 个关系", leaderId, cleanupCount);
            return cleanupCount;
            
        } catch (Exception e) {
            log.error("清除带单员ID: {} 的跟单关系失败", leaderId, e);
            throw new RuntimeException("清除跟单关系失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean checkAndCleanupSingleFollowRelation(Long followerId, Long leaderId) {
        try {
            log.debug("检查单个跟单关系 - 跟单用户ID: {}, 带单员ID: {}", followerId, leaderId);
            
            // 1. 查询跟单用户信息
            FrontUser follower = frontUserMapper.selectById(followerId);
            if (follower == null || follower.getIsFollowing() != 1 || !follower.getLeaderId().equals(leaderId)) {
                log.debug("跟单用户ID: {} 不存在或未跟单带单员ID: {}", followerId, leaderId);
                return false;
            }
            
            // 2. 查询带单员配置
            FrontUser leader = frontUserMapper.selectById(leaderId);
            if (leader == null || leader.getCopyConfigId() == null) {
                log.warn("带单员ID: {} 不存在或未配置跟单参数", leaderId);
                return false;
            }
            
            CopyConfig config = copyConfigMapper.selectById(leader.getCopyConfigId());
            if (config == null) {
                log.warn("带单员ID: {} 的配置不存在", leaderId);
                return false;
            }
            
            // 3. 检查余额条件
            boolean shouldCleanup = checkFollowerBalance(follower, config);
            
            if (shouldCleanup) {
                // 清除跟单关系
                cleanupFollowRelation(follower);
                
                log.info("清除单个跟单关系 - 用户ID: {}, 用户名: {}, 余额: {}, 已解锁账户",
                        follower.getId(), follower.getUsername(), follower.getCopyTradeBalance());
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查单个跟单关系失败 - 跟单用户ID: {}, 带单员ID: {}", followerId, leaderId, e);
            throw new RuntimeException("检查跟单关系失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查跟单用户的余额是否满足条件
     */
    private boolean checkFollowerBalance(FrontUser follower, CopyConfig config) {
        // BigDecimal balance = follower.getCopyTradeBalance();
        //这里要重新获取他的跟单账户余额
          BigDecimal balance =    frontUserMapper.getCopyTradeBalanceById(follower.getId());
        if (balance == null) {
            balance = BigDecimal.ZERO;
        }
        
        // 检查最低跟单金额
        if (balance.compareTo(config.getMinFollowAmount()) < 0) {
            log.debug("用户ID: {} 余额 {} 低于最低跟单金额 {}", 
                    follower.getId(), balance, config.getMinFollowAmount());
            return true; // 需要清除
        }
        
        // 检查最高跟单金额（如果设置了限制）
        if (config.getMaxFollowAmount().compareTo(BigDecimal.ZERO) > 0 && 
            balance.compareTo(config.getMaxFollowAmount()) > 0) {
            log.debug("用户ID: {} 余额 {} 高于最高跟单金额 {}", 
                    follower.getId(), balance, config.getMaxFollowAmount());
            return true; // 需要清除
        }
        
        return false; // 余额满足条件，不需要清除
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupInvalidFollowRelationsAfterSettlement(Long leaderId) {
        try {
            log.info("开始清除带单员ID: {} 的无效跟单关系（结算后版本）", leaderId);

            // 1. 查询所有跟随该带单员的用户
            List<FrontUser> followers = frontUserMapper.selectFollowersByLeaderId(leaderId);

            if (followers.isEmpty()) {
                log.info("带单员ID: {} 没有跟单用户", leaderId);
                return 0;
            }

            log.info("找到 {} 个跟单用户，开始验证余额条件", followers.size());

            // 2. 获取带单员的配置信息
            FrontUser leader = frontUserMapper.selectById(leaderId);
            if (leader == null || leader.getCopyConfigId() == null) {
                log.warn("带单员ID: {} 不存在或未配置跟单参数", leaderId);
                return 0;
            }

            CopyConfig config = copyConfigMapper.selectById(leader.getCopyConfigId());
            if (config == null) {
                log.warn("带单员ID: {} 的配置不存在", leaderId);
                return 0;
            }

            log.info("带单员配置 - 最低跟单金额: {}, 最高跟单金额: {}",
                    config.getMinFollowAmount(), config.getMaxFollowAmount());

            int cleanupCount = 0;

            // 3. 逐个检查跟单用户的余额条件
            for (FrontUser follower : followers) {
                boolean shouldCleanup = checkFollowerBalance(follower, config);

                if (shouldCleanup) {
                    // 清除跟单关系（只改状态，不影响余额）
                    cleanupFollowRelationStatusOnly(follower);
                    cleanupCount++;

                    log.info("清除跟单关系（仅状态）- 用户ID: {}, 用户名: {}, 余额: {}, 原因: 不满足跟单金额条件",
                            follower.getId(), follower.getUsername(), follower.getCopyTradeBalance());
                }
            }

            log.info("带单员ID: {} 的跟单关系清除完成（结算后版本），共清除 {} 个关系", leaderId, cleanupCount);
            return cleanupCount;

        } catch (Exception e) {
            log.error("清除带单员ID: {} 的跟单关系失败（结算后版本）", leaderId, e);
            throw new RuntimeException("清除跟单关系失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清除跟单关系
     */
    private void cleanupFollowRelation(FrontUser follower) {
        // 使用SQL命令直接更新，只修改跟单相关字段，不影响其他字段
        int updateResult = frontUserMapper.clearFollowRelationStatusOnly(follower.getId());
        if (updateResult != 1) {
            throw new RuntimeException("更新用户跟单状态失败，用户ID: " + follower.getId());
        }

        log.info("已清除用户ID: {} 的跟单关系，解锁账户状态", follower.getId());
    }

    /**
     * 清除跟单关系（仅修改状态，不影响账户余额）
     * 用于结算完成后的关系清除，确保佣金已经分配完成
     */
    private void cleanupFollowRelationStatusOnly(FrontUser follower) {
        // 使用SQL命令直接更新，只修改跟单状态，不影响账户余额和锁定状态
        int updateResult = frontUserMapper.clearFollowRelationStatusOnly(follower.getId());
        if (updateResult != 1) {
            throw new RuntimeException("更新用户跟单状态失败，用户ID: " + follower.getId());
        }

        log.info("已清除用户ID: {} 的跟单关系（仅状态），账户余额和锁定状态保持不变", follower.getId());
    }
}

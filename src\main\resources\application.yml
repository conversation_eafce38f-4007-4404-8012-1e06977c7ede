server:
  port: 8094
spring:
  datasource:
    url: ************************************************************************************************************
    username: trading_system
    password: 4F4pes545yLsrmRt
    driver-class-name: com.mysql.cj.jdbc.Driver
  mvc:
    static-path-pattern: /**
  web:
    resources:
      static-locations:
        - classpath:/META-INF/resources/
        - classpath:/resources/
        - classpath:/static/
        - classpath:/public/
        - file:${file.upload.path}
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
  sql:
    init:
      mode: never
      schema-locations: classpath:db/schema.sql
      encoding: UTF-8

  mail:
      host: smtp.163.com
      port: 465
      username: <EMAIL>
      password: JQ2pa35vJSepBEJL
      properties:
        mail:
          smtp:
            auth: true
            ssl:
              enable: true
            socketFactory:
              port: 465
              class: javax.net.ssl.SSLSocketFactory
            starttls:
              enable: true
              required: true

# 文件上传配置
upload:
  env: prod # local=本地，prod=线上
  path-local: E:/data/server/upload
  path-prod: /data/server/upload
  access-path: /upload/**
  allowed-types: jpg,jpeg,png,gif

# JWT配置
jwt:
  secret: alimama7815pl634
  expiration: 8640000000000

# SMS配置
sms:
  api:
    url: 
    userId: 
    password: 
    sign: 
    enterpriseId: 
#分发配置
alipay:
  api:
    GATEWAY:  https://openapi.hzqiuyukj.com
    APP_PRIVATE_KEY:
    OPENAPI_PUBLIC_KEY:
    APP_ID:

# 币安现货接口前缀
binance:
  api-prefix: https://api.binance.com/api

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
  type-aliases-package: com.frontapi.entity

device:
  offline:
    days: 0  

# 自动平仓功能配置
auto-close:
  enabled: true              # 是否启用自动平仓功能
  startup-delay: 10          # 启动延迟时间（秒）
  check-interval: 5          # 检查间隔（秒）
  thread-pool-size: 2        # 线程池大小

# 添加日志配置
logging:
  level:
    root: INFO
    com.frontapi: INFO
    com.frontapi.service.impl.AutoCloseServiceImpl: INFO
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx{short}}"
package com.frontapi.service;

import com.frontapi.dto.RegisterRequest;
import com.frontapi.entity.FrontUser;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.LoginResponse;
import com.frontapi.vo.UserShareVO;
import com.frontapi.vo.UserVO;
import com.frontapi.dto.UpdatePasswordDTO;
import com.frontapi.dto.UpdateSecurityPasswordDTO;
import com.frontapi.dto.UpdatePhoneDTO;
import com.frontapi.vo.TeamStatsVO;
import com.frontapi.vo.TeamRecordVO;
import com.frontapi.vo.PageResult;

public interface UserService {
    /**
     * 用户注册
     * @param request 注册请求
     * @return 注册是否成功
     */
    boolean register(RegisterRequest request);

    LoginResponse login(String phone, String email, String password);
    LoginResponse loginByCode(String phone, String code);
    UserVO getCurrentUserInfo();
    boolean sendPasswordVerifyCode();
    boolean updatePassword(UpdatePasswordDTO dto);
    boolean sendSecurityPasswordVerifyCode();
    boolean updateSecurityPassword(UpdateSecurityPasswordDTO dto);
    boolean sendPhoneVerifyCode(String newPhone);
    boolean updatePhone(UpdatePhoneDTO dto);
    boolean verifySecurityPassword(String securityPassword);
    boolean existsByPhone(String phone); 
    boolean existsByEmail(String email);
    Page<UserShareVO> getShareList(String shareCode, Integer page, Integer size);
    void sendResetCode(String phone, String email);
    void resetPassword(String phone, String email, String code, String newPassword);
    void sendRegisterEmailCode(String email);
    
    /**
     * 执行账户划转
     * @param userId 用户ID
     * @param fromAccountType 转出账户类型
     * @param toAccountType 转入账户类型
     * @param amount 划转金额
     * @param payPassword 支付密码
     * @return 划转是否成功
     */
    boolean executeTransfer(Long userId, String fromAccountType, String toAccountType, BigDecimal amount, String payPassword);

    /**
     * 获取团队业绩统计
     */
    TeamStatsVO getTeamStats(Long userId);

    /**
     * 设置用户利润账户划转开关
     * @param userId 用户ID
     * @param enabled 是否启用(0:禁用,1:启用)
     * @return 设置是否成功
     */
    boolean setProfitTransferEnabled(Long userId, Integer enabled);

    /**
     * 团队记录分页
     */
    PageResult<TeamRecordVO> getTeamRecords(Long userId, String type, Integer page, Integer size);
    void assignCommission(UserVO currentUser, String userNo, BigDecimal assignRate);
    UserVO selectByUserNo(String userNo);
    UserVO getUserByEmail(String email);
    
    /**
     * 校验支付密码
     * @param userId 用户ID
     * @param inputPassword 输入的支付密码
     */
    void checkPayPassword(Long userId, String inputPassword);

    /**
     * 更新用户头像
     */
    void updateAvatar(Long userId, String avatarUrl);
   
} 
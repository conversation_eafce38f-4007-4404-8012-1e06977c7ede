package com.frontapi.service;

import com.frontapi.entity.FrontUser;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.service.impl.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceProfitTransferTest {

    @Mock
    private FrontUserMapper userMapper;

    @InjectMocks
    private UserServiceImpl userService;

    private FrontUser testUser;

    @BeforeEach
    void setUp() {
        testUser = new FrontUser();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setProfitTransferEnabled(1);
    }

    @Test
    void testSetProfitTransferEnabled_Success() {
        // 准备测试数据
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(FrontUser.class))).thenReturn(1);

        // 执行测试
        boolean result = userService.setProfitTransferEnabled(1L, 0);

        // 验证结果
        assertTrue(result);
        assertEquals(Integer.valueOf(0), testUser.getProfitTransferEnabled());
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(testUser);
    }

    @Test
    void testSetProfitTransferEnabled_UserNotFound() {
        // 准备测试数据
        when(userMapper.selectById(1L)).thenReturn(null);

        // 执行测试
        boolean result = userService.setProfitTransferEnabled(1L, 0);

        // 验证结果
        assertFalse(result);
        verify(userMapper).selectById(1L);
        verify(userMapper, never()).updateById(any(FrontUser.class));
    }

    @Test
    void testSetProfitTransferEnabled_UpdateFailed() {
        // 准备测试数据
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(FrontUser.class))).thenReturn(0);

        // 执行测试
        boolean result = userService.setProfitTransferEnabled(1L, 1);

        // 验证结果
        assertFalse(result);
        verify(userMapper).selectById(1L);
        verify(userMapper).updateById(testUser);
    }

    @Test
    void testSetProfitTransferEnabled_EnableTransfer() {
        // 准备测试数据 - 用户当前禁用状态
        testUser.setProfitTransferEnabled(0);
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(FrontUser.class))).thenReturn(1);

        // 执行测试 - 启用划转
        boolean result = userService.setProfitTransferEnabled(1L, 1);

        // 验证结果
        assertTrue(result);
        assertEquals(Integer.valueOf(1), testUser.getProfitTransferEnabled());
    }

    @Test
    void testSetProfitTransferEnabled_DisableTransfer() {
        // 准备测试数据 - 用户当前启用状态
        testUser.setProfitTransferEnabled(1);
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateById(any(FrontUser.class))).thenReturn(1);

        // 执行测试 - 禁用划转
        boolean result = userService.setProfitTransferEnabled(1L, 0);

        // 验证结果
        assertTrue(result);
        assertEquals(Integer.valueOf(0), testUser.getProfitTransferEnabled());
    }
}

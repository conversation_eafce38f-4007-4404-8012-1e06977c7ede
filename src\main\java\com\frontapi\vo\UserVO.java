package com.frontapi.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime; 
 

@Data
public class UserVO {
    private Long id;
    private String userNo;
    private String username;
    private String email;
    private String realName;
    private String phone;
    private String shareCode;
    private String referrerCode;
    private Long referrerId;
    private String avatar;
    
    // 账户余额相关字段
    private BigDecimal availableBalance;
    private BigDecimal copyTradeBalance;
    private BigDecimal commissionBalance;
    private BigDecimal profitBalance;
    private BigDecimal frozenBalance;
    
    // CAT币
    private BigDecimal catBalance;
    
    // 团队统计字段
    private Integer teamTotalCount;
    private Integer teamTodayCount;
    
    // 状态字段
    private Boolean status;
    private Boolean isActivated;
    
    // 佣金相关字段
    private BigDecimal commissionRate;
    private BigDecimal totalRecharge;
    
    // 时间字段
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Integer isLeader;
    private Integer isFollowing;
    private Long copyConfigId;
    private Long leaderId;
    private Integer copyTradeFrozenStatus;

    // 利润账户划转控制
    private Integer profitTransferEnabled;
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.UserMapper"> 

    <select id="selectShareList" resultType="com.frontapi.vo.UserShareVO">
        SELECT 
            CONCAT(LEFT(phone, 3), '*****', RIGHT(phone, 4)) as maskedPhone,
            create_time as createTime,
            team_total_count as teamTotalCount,
            team_today_count as teamTodayCount,
            cat_balance as catBalance
        FROM front_user
        WHERE referrer_code = #{shareCode}
        ORDER BY create_time DESC
    </select>

    <select id="selectTeamRecords" resultType="com.frontapi.vo.TeamRecordVO">
      SELECT user_no AS userNo, username, commission_rate AS commissionRate, create_time AS createTime
      FROM front_user
      WHERE referrer_code = #{shareCode}
      <if test="type == 'valid'">
        AND is_activated = 1
      </if>
      <if test="type == 'today'">
        AND DATE(create_time) = CURDATE()
      </if>
      ORDER BY create_time DESC
    </select>


    <update id="updateCommissionRateByUserNo">
        UPDATE front_user SET commission_rate = #{commissionRate}
        WHERE user_no = #{userNo} AND commission_rate = #{oldRate}
    </update>
    <update id="updateCommissionRateById">
        UPDATE front_user SET commission_rate = #{commissionRate}
        WHERE id = #{id}
    </update>
    <update id="updateAvatar">
        UPDATE front_user SET avatar = #{avatar} WHERE id = #{userId}
    </update>

    <!-- 更新用户登录密码 -->
    <update id="updateUserPassword">
        UPDATE front_user
        SET password = #{password},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 更新用户安全密码 -->
    <update id="updateUserSecurityPassword">
        UPDATE front_user
        SET security_password = #{securityPassword},
            update_time = NOW()
        WHERE id = #{userId}
    </update>
</mapper>
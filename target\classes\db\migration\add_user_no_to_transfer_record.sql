-- 为转账记录表添加用户UID字段
-- 执行时间：2024-07-31

-- 添加转出用户UID字段
ALTER TABLE `transfer_record` ADD COLUMN `from_user_no` varchar(32) DEFAULT NULL COMMENT '转出用户UID' AFTER `from_username`;

-- 添加转入用户UID字段  
ALTER TABLE `transfer_record` ADD COLUMN `to_user_no` varchar(32) DEFAULT NULL COMMENT '转入用户UID' AFTER `to_username`;

-- 更新现有记录的UID字段（从用户表中获取）
UPDATE `transfer_record` tr 
SET `from_user_no` = (
    SELECT fu.user_no 
    FROM front_user fu 
    WHERE fu.id = tr.from_user_id
)
WHERE tr.from_user_no IS NULL;

UPDATE `transfer_record` tr 
SET `to_user_no` = (
    SELECT fu.user_no 
    FROM front_user fu 
    WHERE fu.id = tr.to_user_id
)
WHERE tr.to_user_no IS NULL;

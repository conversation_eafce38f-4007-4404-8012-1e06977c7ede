# 利润账户划转控制功能 API 文档

## 功能概述

本功能为用户表添加了利润账户划转控制开关，管理员可以控制用户是否允许从利润账户向其他账户划转资金。

## 数据库变更

### 新增字段

在 `front_user` 表中新增字段：

```sql
`profit_transfer_enabled` tinyint(4) DEFAULT '1' COMMENT '利润账户划转开关(0:禁止划转,1:允许划转)'
```

### 迁移脚本

执行以下SQL脚本添加新字段：

```sql
-- 添加利润账户划转开关字段
ALTER TABLE `front_user` 
ADD COLUMN `profit_transfer_enabled` tinyint(4) DEFAULT '1' COMMENT '利润账户划转开关(0:禁止划转,1:允许划转)' 
AFTER `leader_id`;

-- 为现有用户设置默认值（允许划转）
UPDATE `front_user` SET `profit_transfer_enabled` = 1 WHERE `profit_transfer_enabled` IS NULL;
```

## API 接口

### 1. 账户划转接口（已修改）

**接口地址：** `POST /api/user/transfer`

**功能变更：** 在原有划转验证基础上，增加了利润账户划转开关的验证。

**验证逻辑：**
- 当 `fromAccountType` 为 `profit` 时，检查用户的 `profit_transfer_enabled` 字段
- 如果 `profit_transfer_enabled` 为 0 或 null，则拒绝划转
- 如果 `profit_transfer_enabled` 为 1，则允许划转

**请求示例：**
```json
{
    "fromAccountType": "profit",
    "toAccountType": "fund",
    "amount": 100.00,
    "payPassword": "123456"
}
```

**响应示例（划转被禁用时）：**
```json
{
    "success": false,
    "message": "该账户类型不支持此划转",
    "data": null
}
```

### 2. 管理员设置利润账户划转开关（新增）

**接口地址：** `POST /api/user/admin/profit-transfer-control`

**功能：** 管理员设置指定用户的利润账户划转开关

**权限要求：** 管理员权限（TODO: 需要实现权限验证）

**请求参数：**
```json
{
    "userId": 123,
    "enabled": 1
}
```

**参数说明：**
- `userId`: 用户ID（必填）
- `enabled`: 开关状态（必填，0=禁用，1=启用）

**响应示例（成功）：**
```json
{
    "success": true,
    "message": "用户利润账户划转已启用",
    "data": null
}
```

**响应示例（失败）：**
```json
{
    "success": false,
    "message": "参数不能为空",
    "data": null
}
```

## 业务逻辑

### 划转规则验证

原有的划转规则保持不变：

1. **资金账户 ↔ 跟单账户**（双向）
2. **佣金账户 → 资金账户或跟单账户**
3. **利润账户 → 资金账户或跟单账户**（新增开关控制）

### 利润账户划转控制

- 当用户尝试从利润账户划转时，系统会检查 `profit_transfer_enabled` 字段
- 只有当该字段值为 1 时，才允许从利润账户划转
- 其他账户类型的划转不受此开关影响

## 实体类变更

### FrontUser 实体类

新增字段：
```java
// 利润账户划转控制
private Integer profitTransferEnabled = 1;
```

### UserVO 类

新增字段：
```java
// 利润账户划转控制
private Integer profitTransferEnabled;
```

## 测试用例

### 单元测试

1. **利润账户划转启用测试**
   - 验证当 `profit_transfer_enabled = 1` 时，利润账户划转成功

2. **利润账户划转禁用测试**
   - 验证当 `profit_transfer_enabled = 0` 时，利润账户划转被拒绝

3. **利润账户划转字段为空测试**
   - 验证当 `profit_transfer_enabled = null` 时，利润账户划转被拒绝

4. **非利润账户划转测试**
   - 验证其他账户类型的划转不受利润账户开关影响

5. **管理员设置开关测试**
   - 验证管理员可以成功设置用户的利润账户划转开关

## 注意事项

1. **默认值：** 新用户的 `profit_transfer_enabled` 默认为 1（允许划转）
2. **向后兼容：** 现有用户在数据库迁移后默认允许利润账户划转
3. **权限控制：** 管理员接口需要添加适当的权限验证（当前为 TODO 状态）
4. **日志记录：** 系统会记录利润账户划转开关的设置操作

## 部署步骤

1. 执行数据库迁移脚本
2. 部署新版本代码
3. 验证功能正常工作
4. 配置管理员权限（如需要）

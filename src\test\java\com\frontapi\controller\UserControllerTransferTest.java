package com.frontapi.controller;

import com.frontapi.entity.FrontUser;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserControllerTransferTest {

    @Mock
    private UserService userService;

    @InjectMocks
    private UserController userController;

    private UserVO testUser;

    @BeforeEach
    void setUp() {
        testUser = new UserVO();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setProfitTransferEnabled(1); // 默认允许划转
        testUser.setProfitBalance(new BigDecimal("1000.0000"));
        testUser.setAvailableBalance(new BigDecimal("500.0000"));
    }

    @Test
    void testProfitTransferEnabled_ShouldAllowTransfer() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("fromAccountType", "profit");
        params.put("toAccountType", "fund");
        params.put("amount", new BigDecimal("100.00"));
        params.put("payPassword", "123456");

        when(userService.getCurrentUserInfo()).thenReturn(testUser);
        when(userService.executeTransfer(anyLong(), anyString(), anyString(), any(BigDecimal.class), anyString()))
                .thenReturn(true);

        // 执行测试
        var response = userController.transfer(params);

        // 验证结果
        assertTrue(response.isSuccess());
        assertEquals("划转成功", response.getMessage());
        verify(userService).executeTransfer(1L, "profit", "fund", new BigDecimal("100.00"), "123456");
    }

    @Test
    void testProfitTransferDisabled_ShouldRejectTransfer() {
        // 设置利润账户划转为禁用
        testUser.setProfitTransferEnabled(0);

        Map<String, Object> params = new HashMap<>();
        params.put("fromAccountType", "profit");
        params.put("toAccountType", "fund");
        params.put("amount", new BigDecimal("100.00"));
        params.put("payPassword", "123456");

        when(userService.getCurrentUserInfo()).thenReturn(testUser);

        // 执行测试
        var response = userController.transfer(params);

        // 验证结果
        assertFalse(response.isSuccess());
        assertEquals("该账户类型不支持此划转", response.getMessage());
        verify(userService, never()).executeTransfer(anyLong(), anyString(), anyString(), any(BigDecimal.class), anyString());
    }

    @Test
    void testProfitTransferNull_ShouldRejectTransfer() {
        // 设置利润账户划转为null（相当于禁用）
        testUser.setProfitTransferEnabled(null);

        Map<String, Object> params = new HashMap<>();
        params.put("fromAccountType", "profit");
        params.put("toAccountType", "copy");
        params.put("amount", new BigDecimal("50.00"));
        params.put("payPassword", "123456");

        when(userService.getCurrentUserInfo()).thenReturn(testUser);

        // 执行测试
        var response = userController.transfer(params);

        // 验证结果
        assertFalse(response.isSuccess());
        assertEquals("该账户类型不支持此划转", response.getMessage());
    }

    @Test
    void testNonProfitTransfer_ShouldNotBeAffected() {
        // 测试非利润账户的划转不受影响
        testUser.setProfitTransferEnabled(0); // 即使禁用利润账户划转

        Map<String, Object> params = new HashMap<>();
        params.put("fromAccountType", "fund");
        params.put("toAccountType", "copy");
        params.put("amount", new BigDecimal("200.00"));
        params.put("payPassword", "123456");

        when(userService.getCurrentUserInfo()).thenReturn(testUser);
        when(userService.executeTransfer(anyLong(), anyString(), anyString(), any(BigDecimal.class), anyString()))
                .thenReturn(true);

        // 执行测试
        var response = userController.transfer(params);

        // 验证结果 - 资金账户到跟单账户的划转应该成功
        assertTrue(response.isSuccess());
        assertEquals("划转成功", response.getMessage());
    }

    @Test
    void testCommissionTransfer_ShouldNotBeAffected() {
        // 测试佣金账户的划转不受利润账户开关影响
        testUser.setProfitTransferEnabled(0); // 禁用利润账户划转

        Map<String, Object> params = new HashMap<>();
        params.put("fromAccountType", "commission");
        params.put("toAccountType", "fund");
        params.put("amount", new BigDecimal("150.00"));
        params.put("payPassword", "123456");

        when(userService.getCurrentUserInfo()).thenReturn(testUser);
        when(userService.executeTransfer(anyLong(), anyString(), anyString(), any(BigDecimal.class), anyString()))
                .thenReturn(true);

        // 执行测试
        var response = userController.transfer(params);

        // 验证结果 - 佣金账户的划转应该成功
        assertTrue(response.isSuccess());
        assertEquals("划转成功", response.getMessage());
    }
}
